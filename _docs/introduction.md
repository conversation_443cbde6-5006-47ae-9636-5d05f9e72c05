# Introduction to Drops.Relation

Drops.Relation is a high-level relation abstraction with extensible architecture built on top of Ecto.SQL, created to accelerate Ecto development in both early stages and as applications scale.

## Architecture Overview

```mermaid
flowchart TD
    %% Main Relation Module
    relationModule["MyApp.Users<br/>Relation Module"]

    %% Core Components
    relationModule --> schema["Drops.Relation.Schema<br/>id: :integer<br/>name: :string<br/>email: :string"]
    relationModule --> queryable["Queryable Protocol<br/>Ecto Query Integration"]
    relationModule --> enumerable["Enumerable Protocol<br/>Enum.to_list(), Enum.count()"]
    relationModule --> ectoSchema["MyApp.Users.User<br/>Auto-generated Ecto Schema"]

    %% Database Connection
    schema -.-> dbTable[("💾 users table<br/>Schema inference")]

    %% Styling
    classDef relationStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef schemaStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef ectoStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef protocolStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef databaseStyle fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class relationModule relationStyle
    class schema schemaStyle
    class ectoSchema ectoStyle
    class queryable,enumerable protocolStyle
    class dbTable databaseStyle
```

## Ecto vs Drops.Relation Comparison

| Plain Ecto | Drops.Relation |
|------------|----------------|
| **Manual schema definition** | **Relation definition with automatic inference** |
| ```elixir<br/>defmodule MyApp.User do<br/>  use Ecto.Schema<br/><br/>  schema "users" do<br/>    field :name, :string<br/>    field :email, :string<br/>    field :active, :boolean<br/>    timestamps()<br/>  end<br/>end<br/>``` | ```elixir<br/>defmodule MyApp.Users do<br/>  use Drops.Relation, otp_app: :my_app<br/><br/>  schema("users", infer: true)<br/>end<br/>``` |
| **Manual schema with custom fields** | **Inference + custom field overrides** |
| ```elixir<br/>defmodule MyApp.User do<br/>  use Ecto.Schema<br/><br/>  schema "users" do<br/>    field :name, :string<br/>    field :email, :string<br/>    field :active, :boolean, default: true<br/>    field :role, :string, default: "member"<br/>    timestamps()<br/>  end<br/>end<br/>``` | ```elixir<br/>defmodule MyApp.Users do<br/>  use Drops.Relation, otp_app: :my_app<br/><br/>  schema("users", infer: true) do<br/>    field(:role, :string, default: "member")<br/>  end<br/>end<br/>``` |
| **Ecto.Repo query functions** | **Relation shortcut methods** |
| ```elixir<br/>users = Repo.all(User)<br/>user = Repo.get(User, 1)<br/>active_users = Repo.all(<br/>  from u in User, where: u.active == true<br/>)<br/>``` | ```elixir<br/>users = MyApp.Users.all()<br/>user = MyApp.Users.get(1)<br/>active_users = MyApp.Users.all_by(active: true)<br/>``` |
| **Custom query definition and composition** | **defquery macro with pipe composition** |
| ```elixir<br/>defmodule MyApp.UserQueries do<br/>  import Ecto.Query<br/><br/>  def active do<br/>    from u in User, where: u.active == true<br/>  end<br/><br/>  def by_role(role) do<br/>    from u in User, where: u.role == ^role<br/>  end<br/>end<br/><br/># Usage<br/>query = User<br/>        \|> MyApp.UserQueries.active()<br/>        \|> order_by(:name)<br/>users = Repo.all(query)<br/>``` | ```elixir<br/>defmodule MyApp.Users do<br/>  use Drops.Relation, otp_app: :my_app<br/><br/>  schema("users", infer: true)<br/><br/>  defquery active() do<br/>    from(u in relation(), where: u.active == true)<br/>  end<br/><br/>  defquery by_role(role) do<br/>    from(u in relation(), where: u.role == ^role)<br/>  end<br/>end<br/><br/># Usage<br/>users = MyApp.Users<br/>        \|> MyApp.Users.active()<br/>        \|> MyApp.Users.order(:name)<br/>        \|> Enum.to_list()<br/>``` |
| **Struct insertion with Ecto.Repo** | **Relation insert method from Writing plugin** |
| ```elixir<br/>changeset = User.changeset(%User{}, %{<br/>  name: "Jane",<br/>  email: "<EMAIL>"<br/>})<br/>{:ok, user} = Repo.insert(changeset)<br/>``` | ```elixir<br/>{:ok, user} = MyApp.Users.insert(%{<br/>  name: "Jane",<br/>  email: "<EMAIL>"<br/>})<br/>``` |
