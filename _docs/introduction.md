# Introduction to Drops.Relation

Drops.Relation is a high-level relation abstraction with extensible architecture built on top of Ecto.SQL, created to accelerate Ecto development in both early stages and as applications scale.

## Architecture Overview

```mermaid
classDiagram
    class MyAppUsers {
        <<Relation Module>>
        +all() List~User~
        +get(id) User
        +insert(attrs) Result~User~
        +update(user, attrs) Result~User~
        +restrict(conditions) Query
        +order(field) Query
        +schema() Schema
    }

    class DropsRelationSchema {
        <<Schema Metadata>>
        +source atom
        +fields List~Field~
        +primary_key List~atom~
        +foreign_keys List~ForeignKey~
        +indices List~Index~
    }

    class Field {
        +name atom
        +type atom
        +meta Map
        +default any
        +virtual boolean
    }

    class MyAppUsersUser {
        <<Auto-generated Ecto Schema>>
        +id integer
        +name string
        +email string
        +active boolean
        +inserted_at naive_datetime
        +updated_at naive_datetime
    }

    class EnumerableProtocol {
        <<Protocol Implementation>>
        +count(relation) integer
        +member(relation, element) boolean
        +reduce(relation, acc, fun) any
    }

    class QueryableProtocol {
        <<Protocol Implementation>>
        +to_query(relation) Query
    }

    class UsersTable {
        <<Database Table>>
        id SERIAL PRIMARY KEY
        name VARCHAR(255)
        email VARCHAR(255)
        active BOOLEAN
        inserted_at TIMESTAMP
        updated_at TIMESTAMP
    }

    %% Relationships
    MyAppUsers --> DropsRelationSchema : uses
    MyAppUsers --> MyAppUsersUser : generates
    MyAppUsers ..|> EnumerableProtocol : implements
    MyAppUsers ..|> QueryableProtocol : implements
    DropsRelationSchema --> Field : contains
    DropsRelationSchema -.-> UsersTable : infers from
    MyAppUsersUser -.-> UsersTable : maps to

    %% Styling
    classDef relationModule fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef schemaModule fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef ectoModule fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef protocolModule fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef databaseModule fill:#f1f8e9,stroke:#689f38,stroke-width:2px

    class MyAppUsers relationModule
    class DropsRelationSchema,Field schemaModule
    class MyAppUsersUser ectoModule
    class EnumerableProtocol,QueryableProtocol protocolModule
    class UsersTable databaseModule
```

## Ecto vs Drops.Relation Comparison

### Schema Definition

<!-- tabs-open -->

### Plain Ecto

```elixir
defmodule MyApp.User do
  use Ecto.Schema

  schema "users" do
    field :name, :string
    field :email, :string
    field :active, :boolean
    timestamps()
  end
end
```

### Drops.Relation

```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users", infer: true)
end
```

<!-- tabs-close -->

### Schema with Custom Fields

<!-- tabs-open -->

### Plain Ecto

```elixir
defmodule MyApp.User do
  use Ecto.Schema

  schema "users" do
    field :name, :string
    field :email, :string
    field :active, :boolean, default: true
    field :role, :string, default: "member"
    timestamps()
  end
end
```

### Drops.Relation

```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users", infer: true) do
    field(:role, :string, default: "member")
  end
end
```

<!-- tabs-close -->

## Query Functions

<!-- tabs-open -->

### Plain Ecto

```elixir
users = Repo.all(User)

user = Repo.get(User, 1)

active_users = Repo.all_by(User, active: true)
```

### Drops.Relation

```elixir
users = MyApp.Users.all()

user = MyApp.Users.get(1)

active_users = MyApp.Users.all_by(active: true)
```

<!-- tabs-close -->

### Custom Query Definition

<!-- tabs-open -->

### Plain Ecto

```elixir
defmodule MyApp.Users do
  import Ecto.Query

  def active(query) do
    from u in query, where: u.active == true
  end

  def by_role(query, role) do
    from u in query, where: u.role == ^role
  end

  def order(query, field) do
    from u in query, order_by: ^field
  end
end

User
  |> MyApp.Users.active()
  |> MyApp.Users.by_role("member")
  |> MyApp.Users.order_by(:name)
  |> Repo.all()
```

### Drops.Relation

```elixir
defmodule MyApp.Users do
  use Drops.Relation, otp_app: :my_app

  schema("users", infer: true)

  defquery active() do
    from(u in relation(), where: u.active == true)
  end

  defquery by_role(role) do
    from(u in relation(), where: u.role == ^role)
  end
end

MyApp.Users.active()
  |> MyApp.Users.by_role("member")
  |> MyApp.Users.order(:name)
  |> Repo.all()
```

<!-- tabs-close -->
